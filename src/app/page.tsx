'use client';

import { useState } from 'react';
import Hero from './sections/hero';
import Preloader from '@/components/Preloader';
import Navigation from '@/components/Navigation';
import Features from './sections/features';
import AppScreenshots from './sections/app-screenshots';

export default function Home() {
  const [isLoading, setIsLoading] = useState(true);

  const handlePreloaderComplete = () => {
    setIsLoading(false);
  };

  if (isLoading) {
    return <Preloader onComplete={handlePreloaderComplete} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black">
      <Navigation />

      {/* Hero Section */}
      <Hero />

      {/* Features Section */}
      <Features />

      {/* App Screenshots Showcase Section */}
      <AppScreenshots />
    </div>
  );
}
